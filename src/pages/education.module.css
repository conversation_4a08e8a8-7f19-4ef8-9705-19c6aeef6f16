/* Education Solutions Page Styles */

.heroSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 120px 0 80px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.heroSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.heroContent {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.heroSubtitle {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.heroDescription {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  opacity: 0.8;
  line-height: 1.6;
}

.heroCTA {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.ctaButton {
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.ctaPrimary {
  background: white;
  color: #667eea;
}

.ctaPrimary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.ctaSecondary {
  background: transparent;
  color: white;
  border-color: white;
}

.ctaSecondary:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

.section {
  padding: 80px 0;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  color: #2d3748;
}

.sectionDescription {
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #4a5568;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.challengesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.challengeCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-left: 4px solid #e53e3e;
  transition: transform 0.3s ease;
}

.challengeCard:hover {
  transform: translateY(-5px);
}

.challengeIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.challengeTitle {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.challengeDescription {
  color: #4a5568;
  line-height: 1.6;
}

.solutionSection {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.solutionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.solutionCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border-top: 4px solid #667eea;
  transition: all 0.3s ease;
}

.solutionCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.solutionIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  color: #667eea;
}

.solutionTitle {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.solutionDescription {
  color: #4a5568;
  line-height: 1.6;
}

.featuresSection {
  background: white;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.featureCard {
  background: #f7fafc;
  border-radius: 20px;
  padding: 2.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  
}

.featureCard:hover {
  background: white;
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
  transform: translateY(-5px);
}

.featureHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.featureTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.featureSubtitle {
  color: #667eea;
  font-weight: 500;
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.featureList li {
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.featureList li:last-child {
  border-bottom: none;
}

.featureList li::before {
  content: '✨';
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.featureBenefit {
  background: #667eea;
  color: white;
  padding: 1rem;
  border-radius: 12px;
  margin-top: 1.5rem;
  font-weight: 500;
  text-align: center;
}

.benefitsSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

/* User Benefits Section Styles */
.userBenefitsSection {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.benefitsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-top: 3rem;
}

@media (max-width: 768px) {
  .benefitsContainer {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

.benefitsColumn {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.benefitsColumn:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.12);
}

.benefitsHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.benefitsIcon {
  font-size: 2.5rem;
}

.benefitsColumnTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.benefitsList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.benefitItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  background: #f7fafc;
  transition: all 0.3s ease;
}

.benefitItem:hover {
  background: #edf2f7;
  transform: translateX(5px);
}

.benefitIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.benefitContent {
  flex: 1;
}

.benefitTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
}

.benefitDescription {
  font-size: 0.95rem;
  color: #4a5568;
  line-height: 1.5;
  margin: 0;
}

.benefitsHighlight {
  margin-top: 4rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
}

.highlightContent {
  max-width: 800px;
  margin: 0 auto;
}

.highlightTitle {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  color: white;
}

.highlightText {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
  opacity: 0.95;
}

@media (max-width: 768px) {
  .benefitsHighlight {
    margin-top: 3rem;
    padding: 2rem;
  }

  .highlightTitle {
    font-size: 1.5rem;
  }

  .highlightText {
    font-size: 1rem;
  }
}

.benefitCategory {
  background: rgba(255,255,255,0.1);
  border-radius: 20px;
  padding: 2.5rem;
  backdrop-filter: blur(10px);
}

.benefitsContainer {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.benefitCard {
  padding: 2rem;
  padding-bottom: 1rem;
  background-color: var(--light);
  border-radius: 10px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.benefitCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.benefitTitle {
  font-size: 1.5rem;
  font-weight: 700;
  /* margin-bottom: 2rem; */
  text-align: center;
}

.benefitList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefitList li {
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.benefitList li:last-child {
  border-bottom: none;
}

.benefitList li::before {
  content: '🎯';
  flex-shrink: 0;
}

/* Subsection Styles */
.subsectionHeader {
  margin: 4rem 0 2rem 0;
  text-align: center;
}

.subsectionTitle {
  font-size: 2rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.subsectionDescription {
  font-size: 1.1rem;
  color: #4a5568;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Opportunities Grid */
.opportunitiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.opportunityCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-left: 4px solid #38a169;
  transition: transform 0.3s ease;
}

.opportunityCard:hover {
  transform: translateY(-5px);
}

.opportunityIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.opportunityTitle {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.opportunityDescription {
  color: #4a5568;
  line-height: 1.6;
}

/* Collaboration Grid */
.collaborationGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.collaborationCard {
  background: rgba(255,255,255,0.65);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.collaborationCard:hover {
  background: white;
  transform: translateY(-5px);
}

.collaborationIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.collaborationTitle {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.collaborationDescription {
  color: #555;
  line-height: 1.6;
}

/* Higher-Order Thinking Styles */
.thinkingDefinition {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  margin: 3rem 0;
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.definitionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.definitionText {
  font-size: 1.1rem;
  color: #4a5568;
  line-height: 1.7;
  margin: 0;
}

.thinkingLevels {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin: 3rem 0;
}

@media (max-width: 768px) {
  .thinkingLevels {
    grid-template-columns: 1fr;
  }
}

.thinkingLevel {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  position: relative;
}

.thinkingLevel:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.levelHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.levelNumber {
  background: #667eea;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.levelIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.levelTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.levelDescription {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.levelTools {
  background: #f7fafc;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
}

.toolsList {
  color: #667eea;
  font-weight: 500;
}

/* Skills Grid */
.skillsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  /* margin: 2rem 0; */
}

@media (max-width: 768px) {
  .skillsGrid {
    grid-template-columns: 1fr;
  }
}

.skillCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex: 1;
}

.skillCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.skillCard.skill-cognitive::before {
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.skillCard.skill-social::before {
  background: linear-gradient(90deg, #38a169, #68d391);
}

.skillCard.skill-meta::before {
  background: linear-gradient(90deg, #ed8936, #fbb040);
}

.skillCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.skillIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.skillTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.skillDescription {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.skillCategory {
  background: #f7fafc;
  color: #667eea;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  /* font-weight: 500; */
  /* text-transform: uppercase; */
  letter-spacing: 0.5px;
  display: inline-block;
}

/* Skills Help Section */
.skillsHelp {
  background: #f7fafc;
  border-radius: 20px;
  padding: 3rem;
  margin: 4rem 0;
}

.helpTitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 2rem;
}

.helpGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.helpCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.helpCard h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
}

.helpCard p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

.casesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.caseCard {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-top: 4px solid #38a169;
  transition: all 0.3s ease;
}

.caseCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.caseHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.caseIcon {
  font-size: 2.5rem;
}

.caseTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.caseDescription {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.caseResults {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.caseResult {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #2d3748;
  font-weight: 500;
}

.resultIcon {
  color: #38a169;
  flex-shrink: 0;
}



.futureSection {
  background: white;
}

.futureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.futureCard {
  background: #f7fafc;
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.futureCard:hover {
  background: white;
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
  transform: translateY(-5px);
}

.futureIcon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1.5rem;
}

.futureTitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.futureDescription {
  color: #4a5568;
  line-height: 1.6;
}

.ctaSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.ctaContent {
  max-width: 800px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.ctaDescription {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.ctaFeatures {
  font-size: 1rem;
  opacity: 0.8;
  white-space: pre-line;
  line-height: 1.8;
}

/* New Component Styles for Future Education Page */

/* Impact Section */
.impactSection {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.impactGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.impactCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border-top: 4px solid #667eea;
}

.impactCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.impactHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.impactIcon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.impactTitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.impactDescription {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

.impactInsight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  padding: 3rem;
  margin-top: 4rem;
  text-align: center;
}

.insightTitle {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.insightText {
  font-size: 1.1rem;
  line-height: 1.7;
  opacity: 0.9;
  margin: 0;
}

/* Collaboration Section */
.collaborationSection {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.philosophyBox {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  margin: 3rem 0;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border-left: 4px solid #667eea;
}

.philosophyTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.philosophyText {
  font-size: 1.1rem;
  color: #4a5568;
  line-height: 1.7;
  margin: 0;
}

.principlesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.principleCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border-top: 4px solid #667eea;
}

.principleCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.principleIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.principleTitle {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.principleDescription {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

.benefitsContainer {
  margin-top: 4rem;
}

.benefitsTitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 2rem;
}

/* Skills Section */
.skillsSection {
  background: white;
}

.skillsCategoriesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.skillCategory {
  background: #f7fafc;
  border-radius: 20px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.categoryHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.categoryIcon {
  font-size: 2rem;
}

.categoryTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.skillCard {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  /* border-left: 3px solid #667eea; */
}

.skillCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.skillIcon {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.skillTitle {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
}

.skillDescription {
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.5;
  margin: 0;
}

.skillsInsight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  padding: 3rem;
  margin-top: 4rem;
  text-align: center;
}

/* Thinking Section */
.thinkingSection {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.definitionContent {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.definitionVisual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.thinkingPyramid {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  align-items: center;
  padding: 2rem 1rem;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin: 2rem 0;
}

.pyramidLevel {
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  border: 2px solid;
  min-height: 60px;
}

.pyramidLevel:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Pyramid shape - top to bottom (CREATE at top, REMEMBER at bottom) */
.pyramidLevel:nth-child(1) {
  width: 200px;
  background: #d6d8db;
  border-color: #999;
  color: #333;
  font-size: 1rem;
}

.pyramidLevel:nth-child(2) {
  width: 300px;
  background: #e8eaed;
  border-color: #999;
  color: #333;
  font-size: 1rem;
}

.pyramidLevel:nth-child(3) {
  width: 400px;
  background: #f1f3f4;
  border-color: #999;
  color: #333;
  font-size: 1rem;
}

.pyramidLevel:nth-child(4) {
  width: 500px;
  background: #ffffff;
  border-color: #ccc;
  color: #333;
  font-size: 1rem;
}

.pyramidLevel:nth-child(5) {
  width: 600px;
  background: #ffffff;
  border-color: #ccc;
  color: #333;
  font-size: 1rem;
}

.pyramidLevel:nth-child(6) {
  width: 700px;
  background: #ffffff;
  border-color: #ccc;
  color: #333;
  font-size: 1rem;
}

.pyramidLevelTitle {
  font-size: 1.1rem;
  font-weight: 700;
  margin: 0;
}

.pyramidLevelSubtitle {
  font-size: 0.85rem;
  font-weight: 400;
  color: #666;
  margin: 0;
}

.pyramidLabel {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.9rem;
  color: #666;
  font-weight: 600;
  font-style: italic;
}

.thinkingFramework {
  margin: 4rem 0;
}

.frameworkTitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 3rem;
}

.levelExample {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  border-left: 3px solid #667eea;
}

.exampleText {
  font-style: italic;
  color: #4a5568;
}

.thinkingImportance {
  margin-top: 4rem;
}

.importanceTitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 3rem;
}

.importanceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.importanceCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  text-align: center;
}

.importanceCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.importanceIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

/* Solutions Section */
.solutionsSection {
  background: white;
}

.solutionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.solutionLink {
  color: #667eea;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  transition: color 0.3s ease;
}

.solutionLink:hover {
  color: #764ba2;
}

.solutionFeatures {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.solutionFeatures li {
  padding: 0.5rem 0;
  color: #4a5568;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.solutionFeatures li::before {
  content: '✓';
  color: #667eea;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.benefitsMetrics {
  margin-top: 4rem;
}

.metricsTitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 3rem;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.metricCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-5px);
}

.metricIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.metricNumber {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.metricTitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.metricDescription {
  opacity: 0.9;
  font-size: 0.9rem;
  margin: 0;
}

.howItWorks {
  margin-top: 4rem;
  display: flex;
  gap: 3rem;
  align-items: flex-start;
}

.howItWorksImage {
  flex: 1;
  cursor: pointer;
  /* max-width: 500px; */
}

.howItWorksContent {
  flex: 1;
  min-width: 0;
}

.howItWorksTitle {
  font-size: 1.4rem;
  font-weight: 500;
  color: #2d3748;
  text-align: left;
  margin-bottom: 1rem;
}

.workflowSteps {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.workflowStep {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.workflowStep:hover {
  background: white;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.stepHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.stepNumber {
  background: #667eea;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stepTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.stepDescription {
  color: #4a5568;
  line-height: 1.5;
  margin: 0;
  font-size: 0.95rem;
}

/* Transformation Section */
.transformationSection {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.transformationGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.transformationCard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.transformationCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.transformationHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.transformationIcon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.transformationTitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.transformationDescription {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.transformationChanges {
  list-style: none;
  padding: 0;
  margin: 0;
}

.transformationChanges li {
  padding: 0.5rem 0;
  color: #4a5568;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.transformationChanges li::before {
  content: '→';
  color: #667eea;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.transformationVision {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  padding: 3rem;
  margin-top: 4rem;
  text-align: center;
}

.visionTitle {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.visionText {
  font-size: 1.1rem;
  line-height: 1.7;
  opacity: 0.9;
  margin: 0;
}

/* CTA Section */
.ctaSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ctaContent {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.ctaDescription {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.ctaFeatures {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.ctaFeature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.ctaFeature span {
  color: #4ade80;
  font-weight: bold;
}

.featureImage {
  width: 100%;
  padding: 6px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s;
}

.featureImage:hover {
  transform: scale(1.02);
}


/* Responsive Design */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.2rem;
  }

  .heroDescription {
    font-size: 1rem;
  }

  .heroCTA {
    flex-direction: column;
    align-items: center;
  }

  .ctaButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .challengesGrid,
  .solutionGrid,
  .featuresGrid,
  .benefitsGrid,
  .casesGrid,
  .futureGrid,
  .opportunitiesGrid,
  .collaborationGrid,
  .thinkingLevels,
  .skillsGrid,
  .helpGrid,
  .impactGrid,
  .principlesGrid,
  .skillsCategoriesGrid,
  .importanceGrid,
  .solutionsGrid,
  .metricsGrid,
  .transformationGrid {
    grid-template-columns: 1fr;
  }

  .howItWorks {
    flex-direction: column;
    gap: 2rem;
  }

  .howItWorksImage {
    max-width: 100%;
  }

  .howItWorksTitle {
    text-align: center;
  }

  .thinkingDefinition {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1.5rem;
  }

  .pyramidLevel:nth-child(1) { width: 200px; }
  .pyramidLevel:nth-child(2) { width: 160px; }
  .pyramidLevel:nth-child(3) { width: 120px; }
  .pyramidLevel:nth-child(4) { width: 80px; }

  .skillCategory {
    padding: 2rem;
  }

  .ctaFeatures {
    flex-direction: column;
    gap: 1rem;
  }

  .twoColumnGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;
  }

  .section {
    padding: 60px 0;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .ctaTitle {
    font-size: 2rem;
  }

  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }

  .integrationFlow {
    flex-direction: column;
  }

  .flowArrow {
    transform: rotate(90deg);
  }

  .productSolutionsGrid {
    grid-template-columns: 1fr;
  }
}

/* FunBlocks Education Benefits Section */
.benefitsSection {
  /* background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); */
  background: azure;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.benefitCard {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border-top: 4px solid #667eea;
}

.benefitCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.benefitHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.benefitIcon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.benefitTitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.benefitDescription {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.benefitFeatures {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefitFeatures li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: #4a5568;
  line-height: 1.5;
}

.featureCheck {
  color: #38a169;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.benefitsHighlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  padding: 3rem;
  margin-top: 4rem;
  text-align: center;
}

.highlightTitle {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.highlightText {
  font-size: 1.1rem;
  line-height: 1.7;
  opacity: 0.9;
  margin: 0;
}

/* Product Solutions Section */
.productSolutionsSection {
  background: white;
}

.productSolutionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.productCard {
  background: #f7fafc;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.productCard:hover {
  background: white;
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.productHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.productIcon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.productTitle {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.productDescription {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.productFeatures {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.productFeatures li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem 0;
  color: #4a5568;
  line-height: 1.5;
}

.featureIcon {
  color: #667eea;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.productLink {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.3s ease;
}

.productLink:hover {
  color: #764ba2;
}

.solutionIntegration {
  background: #f7fafc;
  border-radius: 20px;
  padding: 3rem;
  margin-top: 4rem;
}

.integrationTitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 3rem;
}

.integrationFlow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.flowStep {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  flex: 1;
  min-width: 200px;
  max-width: 250px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.stepIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.flowStep h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.flowStep p {
  color: #4a5568;
  line-height: 1.5;
  margin: 0;
  font-size: 0.95rem;
}

.flowArrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

.solutionBenefits {
  margin-top: 4rem;
}

.benefitsTitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
  margin-bottom: 3rem;
}

.benefitItem {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.benefitItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.benefitItem h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.benefitItem p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}
