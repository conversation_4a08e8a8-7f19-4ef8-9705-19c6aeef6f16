import React, { useState } from 'react';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Heading from '@theme/Heading';
import Link from '@docusaurus/Link';
import clsx from 'clsx';
import styles from './education.module.css';
import BloomPyramid from '../components/BloomPyramid';
import EducationStructuredData from '../components/EducationStructuredData';
import EducationFAQ from '../components/EducationFAQ';
import Footer from '../components/Footer';
import ImageModal from '../components/ImageModal';

// Hero Section Component
function HeroSection() {
  return (
    <section className={styles.heroSection}>
      <div className="container">
        <div className={styles.heroContent}>
          <Heading as="h1" className={styles.heroTitle}>
            <Translate id="education.future.hero.title">
              FunBlocks AI: Shaping the Future of Education
            </Translate>
          </Heading>
          <p className={styles.heroSubtitle}>
            <Translate id="education.future.hero.subtitle">
              Human-AI Collaboration for Next-Generation Learning
            </Translate>
          </p>
          <p className={styles.heroDescription}>
            <Translate id="education.future.hero.description">
              In the AI era, education faces unprecedented transformation. FunBlocks AI empowers educators and learners with advanced thinking tools to develop essential skills for the future, fostering human-AI collaboration that enhances rather than replaces human intelligence.
            </Translate>
          </p>
          <div className={styles.heroCTA}>
            <Link
              to="#products-solutions"
              className={clsx(styles.ctaButton, styles.ctaPrimary)}
            >
              <Translate id="education.future.hero.cta.primary">Explore Solutions</Translate>
            </Link>
            <Link
              to="https://www.funblocks.net/"
              className={clsx(styles.ctaButton, styles.ctaSecondary)}
            >
              <Translate id="education.future.hero.cta.secondary">See AI in Action</Translate>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}

// FunBlocks AI Education Benefits Section Component
function FunBlocksEducationBenefitsSection() {
  const benefits = [
    {
      icon: '👩‍🏫',
      titleId: 'education.solutions.teachers.title',
      descriptionId: 'education.solutions.teachers.description',
      features: [
        'education.solutions.teachers.feature1',
        'education.solutions.teachers.feature2',
        'education.solutions.teachers.feature3',
        'education.solutions.teachers.feature4'
      ],
      category: 'teachers'
    },
    {
      icon: '💡',
      titleId: 'education.solutions.students.title',
      descriptionId: 'education.solutions.students.description',
      features: [
        'education.solutions.students.feature1',
        'education.solutions.students.feature2',
        'education.solutions.students.feature3',
        'education.solutions.students.feature4'
      ],
      category: 'students'
    },
    {
      icon: '🤝',
      titleId: 'education.solutions.collaboration.title',
      descriptionId: 'education.solutions.collaboration.description',
      features: [
        'education.solutions.collaboration.feature1',
        'education.solutions.collaboration.feature2',
        'education.solutions.collaboration.feature3',
        'education.solutions.collaboration.feature4'
      ],
      category: 'collaboration'
    }
  ];

  return (
    <section className={clsx(styles.section, styles.benefitsSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.solutions.title">
            FunBlocks AI Education Solutions: Transforming Learning
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.solutions.description">
            Our comprehensive AI education platform combines cutting-edge technology with proven educational science to deliver transformative learning experiences for both educators and students.
          </Translate>
        </p>

        <div className={styles.benefitsGrid}>
          {benefits.map((benefit, index) => (
            <div key={index} className={clsx(styles.benefitCard, styles[`benefit-${benefit.category}`])}>
              <div className={styles.benefitHeader}>
                <span className={styles.benefitIcon}>{benefit.icon}</span>
                <h3 className={styles.benefitTitle}>
                  <Translate id={benefit.titleId} />
                </h3>
              </div>
              <p className={styles.benefitDescription}>
                <Translate id={benefit.descriptionId} />
              </p>
              <ul className={styles.benefitFeatures}>
                {benefit.features.map((featureId, featureIndex) => (
                  <li key={featureIndex}>
                    <span className={styles.featureCheck}>✓</span>
                    <Translate id={featureId} />
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className={styles.benefitsHighlight}>
          <div className={styles.highlightContent}>
            <h3 className={styles.highlightTitle}>
              <Translate id="education.solutions.highlight.title">
                The Perfect Blend: AI Technology + Educational Science
              </Translate>
            </h3>
            <p className={styles.highlightText}>
              <Translate id="education.solutions.highlight.text">
                FunBlocks AI doesn't just use AI for the sake of technology. Every feature is grounded in educational research, from Bloom's Taxonomy to modern cognitive science, ensuring that AI truly enhances learning rather than replacing human thinking.
              </Translate>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

// Benefits for Learners and Educators Section Component
function BenefitsForUsersSection() {
  const learnerBenefits = [
    {
      icon: '🎯',
      titleId: 'education.benefits.learners.critical.title',
      descriptionId: 'education.benefits.learners.critical.description',
    },
    {
      icon: '🧠',
      titleId: 'education.benefits.learners.thinking.title',
      descriptionId: 'education.benefits.learners.thinking.description',
    },
    {
      icon: '🎨',
      titleId: 'education.benefits.learners.creativity.title',
      descriptionId: 'education.benefits.learners.creativity.description',
    },
    {
      icon: '🔍',
      titleId: 'education.benefits.learners.analysis.title',
      descriptionId: 'education.benefits.learners.analysis.description',
    },
    {
      icon: '🤝',
      titleId: 'education.benefits.learners.collaboration.title',
      descriptionId: 'education.benefits.learners.collaboration.description',
    },
    {
      icon: '📈',
      titleId: 'education.benefits.learners.growth.title',
      descriptionId: 'education.benefits.learners.growth.description',
    }
  ];

  const educatorBenefits = [
    {
      icon: '⚡',
      titleId: 'education.benefits.educators.efficiency.title',
      descriptionId: 'education.benefits.educators.efficiency.description',
    },
    {
      icon: '🎯',
      titleId: 'education.benefits.educators.personalization.title',
      descriptionId: 'education.benefits.educators.personalization.description',
    },
    {
      icon: '📊',
      titleId: 'education.benefits.educators.insights.title',
      descriptionId: 'education.benefits.educators.insights.description',
    },
    {
      icon: '🛠️',
      titleId: 'education.benefits.educators.tools.title',
      descriptionId: 'education.benefits.educators.tools.description',
    },
    {
      icon: '💡',
      titleId: 'education.benefits.educators.innovation.title',
      descriptionId: 'education.benefits.educators.innovation.description',
    },
    {
      icon: '🎓',
      titleId: 'education.benefits.educators.development.title',
      descriptionId: 'education.benefits.educators.development.description',
    }
  ];

  return (
    <section className={clsx(styles.section, styles.userBenefitsSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.benefits.title">
            Transformative Benefits for Every User
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.benefits.description">
            FunBlocks AI delivers measurable improvements for both learners and educators, enhancing the educational experience through intelligent technology that amplifies human potential.
          </Translate>
        </p>

        <div className={styles.benefitsContainer}>
          {/* Learners Benefits */}
          <div className={styles.benefitsColumn}>
            <div className={styles.benefitsHeader}>
              <span className={styles.benefitsIcon}>🎓</span>
              <h3 className={styles.benefitsColumnTitle}>
                <Translate id="education.benefits.learners.title">
                  Benefits for Learners
                </Translate>
              </h3>
            </div>
            <div className={styles.benefitsList}>
              {learnerBenefits.map((benefit, index) => (
                <div key={index} className={styles.benefitItem}>
                  <span className={styles.benefitIcon}>{benefit.icon}</span>
                  <div className={styles.benefitContent}>
                    <h4 className={styles.benefitTitle}>
                      <Translate id={benefit.titleId} />
                    </h4>
                    <p className={styles.benefitDescription}>
                      <Translate id={benefit.descriptionId} />
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Educators Benefits */}
          <div className={styles.benefitsColumn}>
            <div className={styles.benefitsHeader}>
              <span className={styles.benefitsIcon}>👩‍🏫</span>
              <h3 className={styles.benefitsColumnTitle}>
                <Translate id="education.benefits.educators.title">
                  Benefits for Educators
                </Translate>
              </h3>
            </div>
            <div className={styles.benefitsList}>
              {educatorBenefits.map((benefit, index) => (
                <div key={index} className={styles.benefitItem}>
                  <span className={styles.benefitIcon}>{benefit.icon}</span>
                  <div className={styles.benefitContent}>
                    <h4 className={styles.benefitTitle}>
                      <Translate id={benefit.titleId} />
                    </h4>
                    <p className={styles.benefitDescription}>
                      <Translate id={benefit.descriptionId} />
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className={styles.benefitsHighlight}>
          <div className={styles.highlightContent}>
            <h3 className={styles.highlightTitle}>
              <Translate id="education.benefits.highlight.title">
                Proven Results Across Educational Settings
              </Translate>
            </h3>
            <p className={styles.highlightText}>
              <Translate id="education.benefits.highlight.text">
                From K-12 classrooms to higher education institutions, FunBlocks AI has demonstrated consistent improvements in learning outcomes, teaching efficiency, and student engagement. Our evidence-based approach ensures that every feature contributes to meaningful educational transformation.
              </Translate>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

// AI Era Impact Section Component
function AIEraImpactSection() {
  const impacts = [
    {
      icon: '🌍',
      titleId: 'education.future.impact.society.title',
      descriptionId: 'education.future.impact.society.description',
      category: 'society'
    },
    {
      icon: '👤',
      titleId: 'education.future.impact.individual.title',
      descriptionId: 'education.future.impact.individual.description',
      category: 'individual'
    },
    {
      icon: '🏢',
      titleId: 'education.future.impact.workplace.title',
      descriptionId: 'education.future.impact.workplace.description',
      category: 'workplace'
    },
    {
      icon: '🎓',
      titleId: 'education.future.impact.education.title',
      descriptionId: 'education.future.impact.education.description',
      category: 'education'
    },
  ];

  return (
    <section className={clsx(styles.section, styles.impactSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.future.impact.title">
            The AI Revolution: Transforming Our World
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.future.impact.description">
            Artificial Intelligence is reshaping every aspect of human life, creating both unprecedented opportunities and fundamental challenges that require new approaches to education and learning.
          </Translate>
        </p>

        <div className={styles.impactGrid}>
          {impacts.map((impact, index) => (
            <div key={index} className={clsx(styles.impactCard, styles[`impact-${impact.category}`])}>
              <div className={styles.impactHeader}>
                <span className={styles.impactIcon}>{impact.icon}</span>
                <h3 className={styles.impactTitle}>
                  <Translate id={impact.titleId} />
                </h3>
              </div>
              <p className={styles.impactDescription}>
                <Translate id={impact.descriptionId} />
              </p>
            </div>
          ))}
        </div>

        <div className={styles.impactInsight}>
          <div className={styles.insightContent}>
            <h3 className={styles.insightTitle}>
              <Translate id="education.future.impact.insight.title">
                The Question Isn't Whether AI Will Change Education
              </Translate>
            </h3>
            <p className={styles.insightText}>
              <Translate id="education.future.impact.insight.text">
                The question is: How can we prepare learners to thrive in an AI-augmented world while preserving and enhancing uniquely human capabilities?
              </Translate>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

// Human-AI Collaboration Section Component
function HumanAICollaborationSection() {
  const collaborationPrinciples = [
    {
      icon: '🤝',
      titleId: 'education.future.collaboration.partnership.title',
      descriptionId: 'education.future.collaboration.partnership.description',
      highlight: 'partnership'
    },
    {
      icon: '💡',
      titleId: 'education.future.collaboration.augmentation.title',
      descriptionId: 'education.future.collaboration.augmentation.description',
      highlight: 'augmentation'
    },
    {
      icon: '⚖️',
      titleId: 'education.future.collaboration.balance.title',
      descriptionId: 'education.future.collaboration.balance.description',
      highlight: 'balance'
    },
  ];

  const collaborationBenefits = [
    {
      icon: '🎯',
      titleId: 'education.future.collaboration.benefits.efficiency.title',
      descriptionId: 'education.future.collaboration.benefits.efficiency.description',
    },
    {
      icon: '💡',
      titleId: 'education.future.collaboration.benefits.creativity.title',
      descriptionId: 'education.future.collaboration.benefits.creativity.description',
    },
    {
      icon: '🌱',
      titleId: 'education.future.collaboration.benefits.growth.title',
      descriptionId: 'education.future.collaboration.benefits.growth.description',
    },
  ];

  return (
    <section className={clsx(styles.section, styles.collaborationSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.future.collaboration.title">
            Human-AI Collaboration: The Heart of Future Education
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.future.collaboration.description">
            The future of education lies not in replacing humans with AI, but in creating powerful partnerships where AI amplifies human intelligence, creativity, and wisdom.
          </Translate>
        </p>

        {/* Core Philosophy */}
        <div className={styles.philosophyBox}>
          <h3 className={styles.philosophyTitle}>
            <Translate id="education.future.collaboration.philosophy.title">
              Our Philosophy: Human-Centered AI
            </Translate>
          </h3>
          <p className={styles.philosophyText}>
            <Translate id="education.future.collaboration.philosophy.text">
              AI should enhance human capabilities, not replace them. In education, this means creating tools that help teachers teach better and students learn deeper, while preserving the irreplaceable human elements of empathy, creativity, and critical thinking.
            </Translate>
          </p>
        </div>

        {/* Collaboration Principles */}
        <div className={styles.principlesGrid}>
          {collaborationPrinciples.map((principle, index) => (
            <div key={index} className={clsx(styles.principleCard, styles[`principle-${principle.highlight}`])}>
              <span className={styles.principleIcon}>{principle.icon}</span>
              <h4 className={styles.principleTitle}>
                <Translate id={principle.titleId} />
              </h4>
              <p className={styles.principleDescription}>
                <Translate id={principle.descriptionId} />
              </p>
            </div>
          ))}
        </div>

        {/* Collaboration Benefits */}
        <div>
          <h3 className={styles.benefitsTitle}>
            <Translate id="education.future.collaboration.benefits.title">
              Why Human-AI Collaboration Works
            </Translate>
          </h3>
          <div className={styles.principlesGrid}>
            {collaborationBenefits.map((benefit, index) => (
              <div key={index} className={styles.benefitCard}>
                <h4 className={styles.benefitTitle}>
                  <Translate id={benefit.titleId} />
                </h4>
                <p className={styles.benefitDescription}>
                  <Translate id={benefit.descriptionId} />
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

// Essential Skills for AI Era Section Component
function AIEraSkillsSection() {
  const skillCategories = [
    {
      categoryId: 'education.future.skills.cognitive.category',
      icon: '💡',
      skills: [
        {
          titleId: 'education.future.skills.critical.title',
          descriptionId: 'education.future.skills.critical.description',
          icon: '🔍'
        },
        {
          titleId: 'education.future.skills.creative.title',
          descriptionId: 'education.future.skills.creative.description',
          icon: '🎨'
        },
        {
          titleId: 'education.future.skills.analytical.title',
          descriptionId: 'education.future.skills.analytical.description',
          icon: '📊'
        }
      ]
    },
    {
      categoryId: 'education.future.skills.social.category',
      icon: '🤝',
      skills: [
        {
          titleId: 'education.future.skills.emotional.title',
          descriptionId: 'education.future.skills.emotional.description',
          icon: '❤️'
        },
        {
          titleId: 'education.future.skills.collaboration.title',
          descriptionId: 'education.future.skills.collaboration.description',
          icon: '👥'
        },
        {
          titleId: 'education.future.skills.communication.title',
          descriptionId: 'education.future.skills.communication.description',
          icon: '💬'
        }
      ]
    },
    {
      categoryId: 'education.future.skills.meta.category',
      icon: '🎯',
      skills: [
        {
          titleId: 'education.future.skills.adaptability.title',
          descriptionId: 'education.future.skills.adaptability.description',
          icon: '🔄'
        },
        {
          titleId: 'education.future.skills.aiLiteracy.title',
          descriptionId: 'education.future.skills.aiLiteracy.description',
          icon: '🤖'
        },
        {
          titleId: 'education.future.skills.learning.title',
          descriptionId: 'education.future.skills.learning.description',
          icon: '📚'
        }
      ]
    }
  ];

  return (
    <section className={clsx(styles.section, styles.skillsSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.future.skills.title">
            Essential Human Qualities for the AI Era
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.future.skills.description">
            As AI transforms our world, certain uniquely human capabilities become more valuable than ever. These skills complement AI's strengths and ensure humans remain at the center of meaningful work and learning.
          </Translate>
        </p>

        <div className={styles.skillsCategoriesGrid}>
          {skillCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className={styles.skillCategory}>
              <div className={styles.categoryHeader}>
                <span className={styles.categoryIcon}>{category.icon}</span>
                <h3 className={styles.categoryTitle}>
                  <Translate id={category.categoryId} />
                </h3>
              </div>
              <div className={styles.skillsGrid}>
                {category.skills.map((skill, skillIndex) => (
                  <div key={skillIndex} className={styles.skillCard}>
                    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                      <span className={styles.skillIcon}>{skill.icon}</span>
                      <h4 className={styles.skillTitle}>
                        <Translate id={skill.titleId} />
                      </h4>
                    </div>
                    <p className={styles.skillDescription}>
                      <Translate id={skill.descriptionId} />
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className={styles.skillsInsight}>
          <h3 className={styles.insightTitle}>
            <Translate id="education.future.skills.insight.title">
              The Human Advantage in an AI World
            </Translate>
          </h3>
          <p className={styles.insightText}>
            <Translate id="education.future.skills.insight.text">
              While AI excels at processing information and pattern recognition, humans bring irreplaceable qualities: empathy, ethical reasoning, creative problem-solving, and the ability to find meaning and purpose. The future belongs to those who can effectively combine these human strengths with AI capabilities.
            </Translate>
          </p>
        </div>
      </div>
    </section>
  );
}

// Higher-Order Thinking Section Component
function HigherOrderThinkingSection({ setShowImageSrc }) {
  const thinkingFramework = [
    {
      level: '01',
      titleId: 'education.future.thinking.analysis.title',
      descriptionId: 'education.future.thinking.analysis.description',
      exampleId: 'education.future.thinking.analysis.example',
      icon: '🔍',
      color: 'blue'
    },
    {
      level: '02',
      titleId: 'education.future.thinking.synthesis.title',
      descriptionId: 'education.future.thinking.synthesis.description',
      exampleId: 'education.future.thinking.synthesis.example',
      icon: '🔗',
      color: 'green'
    },
    {
      level: '03',
      titleId: 'education.future.thinking.evaluation.title',
      descriptionId: 'education.future.thinking.evaluation.description',
      exampleId: 'education.future.thinking.evaluation.example',
      icon: '⚖️',
      color: 'orange'
    },
    {
      level: '04',
      titleId: 'education.future.thinking.creation.title',
      descriptionId: 'education.future.thinking.creation.description',
      exampleId: 'education.future.thinking.creation.example',
      icon: '💡',
      color: 'purple'
    },
  ];


  return (
    <section className={clsx(styles.section, styles.thinkingSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.future.thinking.title">
            Higher-Order Thinking: The Foundation of Future Learning
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.future.thinking.description">
            In an age where AI can process information instantly, the ability to think critically, creatively, and analytically becomes humanity's greatest asset. Higher-order thinking skills are what separate human intelligence from artificial intelligence.
          </Translate>
        </p>

        {/* Definition and Importance */}
        <div className={styles.thinkingDefinition}>
          <div className={styles.definitionContent}>
            <h3 className={styles.definitionTitle}>
              <Translate id="education.future.thinking.definition.title">
                What Makes Thinking "Higher-Order"?
              </Translate>
            </h3>
            <p className={styles.definitionText}>
              <Translate id="education.future.thinking.definition.text">
                Higher-order thinking goes beyond memorization and basic comprehension. It involves analyzing complex information, synthesizing ideas from multiple sources, evaluating evidence and arguments, and creating original solutions. These cognitive processes are essential for navigating an AI-enhanced world.
              </Translate>
            </p>
          </div>
          <div className={styles.definitionVisual}>
            <div style={{ cursor: 'pointer' }}>
              <img
                className={styles.featureImage}
                src="/img/portfolio/thumbnails/bloom_taxonomy_levels.png"
                alt="AI Slides: Effortless slide creation with Markdown, AI, and cloud collaboration"
                onClick={() => setShowImageSrc("/img/portfolio/fullsize/bloom_taxonomy_levels.png")}
              />
            </div>
          </div>
        </div>

        {/* Thinking Framework */}
        <div className={styles.thinkingFramework}>
          <h3 className={styles.frameworkTitle}>
            <Translate id="education.future.thinking.framework.title">
              The Four Pillars of Higher-Order Thinking
            </Translate>
          </h3>
          <div className={styles.thinkingLevels}>
            {thinkingFramework.map((level, index) => (
              <div key={index} className={clsx(styles.thinkingLevel, styles[`level-${level.color}`])}>
                <div className={styles.levelHeader}>
                  <span className={styles.levelNumber}>{level.level}</span>
                  <span className={styles.levelIcon}>{level.icon}</span>
                  <h4 className={styles.levelTitle}>
                    <Translate id={level.titleId} />
                  </h4>
                </div>
                <p className={styles.levelDescription}>
                  <Translate id={level.descriptionId} />
                </p>
                <div className={styles.levelExample}>
                  <strong>
                    <Translate id="education.future.thinking.example.label">
                      Example:
                    </Translate>
                  </strong>
                  <span className={styles.exampleText}>
                    <Translate id={level.exampleId} />
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Why It Matters */}
        <div className={styles.thinkingImportance}>
          <h3 className={styles.importanceTitle}>
            <Translate id="education.future.thinking.importance.title">
              Why Higher-Order Thinking Matters More Than Ever
            </Translate>
          </h3>
          <div className={styles.importanceGrid}>
            <div className={styles.importanceCard}>
              <span className={styles.importanceIcon}>🚀</span>
              <h4>
                <Translate id="education.future.thinking.importance.innovation.title">
                  Drives Innovation
                </Translate>
              </h4>
              <p>
                <Translate id="education.future.thinking.importance.innovation.description">
                  Creative and critical thinking leads to breakthrough solutions that AI alone cannot generate
                </Translate>
              </p>
            </div>
            <div className={styles.importanceCard}>
              <span className={styles.importanceIcon}>🛡️</span>
              <h4>
                <Translate id="education.future.thinking.importance.resilience.title">
                  Builds Resilience
                </Translate>
              </h4>
              <p>
                <Translate id="education.future.thinking.importance.resilience.description">
                  Analytical skills help navigate uncertainty and adapt to rapid technological change
                </Translate>
              </p>
            </div>
            <div className={styles.importanceCard}>
              <span className={styles.importanceIcon}>🎯</span>
              <h4>
                <Translate id="education.future.thinking.importance.purpose.title">
                  Provides Purpose
                </Translate>
              </h4>
              <p>
                <Translate id="education.future.thinking.importance.purpose.description">
                  Evaluative thinking helps determine what matters most and guides ethical decision-making
                </Translate>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// FunBlocks AI Solutions Section Component
function FunBlocksSolutionsSection() {
  const solutions = [
    {
      titleId: 'education.future.solutions.aiflow.title',
      descriptionId: 'education.future.solutions.aiflow.description',
      features: [
        'education.future.solutions.aiflow.feature1',
        'education.future.solutions.aiflow.feature2',
        'education.future.solutions.aiflow.feature3'
      ],
      icon: '💡',
      link: 'https://www.funblocks.net/aiflow',
      category: 'thinking'
    },
    {
      titleId: 'education.future.solutions.workspace.title',
      descriptionId: 'education.future.solutions.workspace.description',
      features: [
        'education.future.solutions.workspace.feature1',
        'education.future.solutions.workspace.feature2',
        'education.future.solutions.workspace.feature3'
      ],
      icon: '🛠️',
      link: 'https://www.funblocks.net',
      category: 'productivity'
    },
    {
      titleId: 'education.future.solutions.tools.title',
      descriptionId: 'education.future.solutions.tools.description',
      features: [
        'education.future.solutions.tools.feature1',
        'education.future.solutions.tools.feature2',
        'education.future.solutions.tools.feature3'
      ],
      icon: '🎯',
      link: 'https://www.funblocks.net/aitools',
      category: 'education'
    }
  ];

  const benefits = [
    {
      titleId: 'education.future.solutions.benefits.efficiency.title',
      descriptionId: 'education.future.solutions.benefits.efficiency.description',
      icon: '⚡',
      metric: '70%'
    },
    {
      titleId: 'education.future.solutions.benefits.engagement.title',
      descriptionId: 'education.future.solutions.benefits.engagement.description',
      icon: '🎯',
      metric: '85%'
    },
    {
      titleId: 'education.future.solutions.benefits.thinking.title',
      descriptionId: 'education.future.solutions.benefits.thinking.description',
      icon: '💡',
      metric: '60%'
    }
  ];

  return (
    <section className={clsx(styles.section, styles.solutionsSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.future.solutions.title">
            FunBlocks AI: Empowering Higher-Order Thinking
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.future.solutions.description">
            Our comprehensive AI-powered platform is specifically designed to enhance human thinking capabilities, not replace them. Every tool is built with educational theory and cognitive science principles at its core.
          </Translate>
        </p>

        {/* Solutions Grid */}
        {/* <div className={styles.solutionsGrid}>
          {solutions.map((solution, index) => (
            <div key={index} className={clsx(styles.solutionCard, styles[`solution-${solution.category}`])}>
              <div className={styles.solutionHeader}>
                <span className={styles.solutionIcon}>{solution.icon}</span>
                <h3 className={styles.solutionTitle}>
                  <Translate id={solution.titleId} />
                </h3>
              </div>
              <p className={styles.solutionDescription}>
                <Translate id={solution.descriptionId} />
              </p>
              <ul className={styles.solutionFeatures}>
                {solution.features.map((featureId, featureIndex) => (
                  <li key={featureIndex}>
                    <Translate id={featureId} />
                  </li>
                ))}
              </ul>
              <Link to={solution.link} className={styles.solutionLink}>
                <Translate id="education.future.solutions.explore">
                  Explore →
                </Translate>
              </Link>
            </div>
          ))}
        </div> */}

        {/* Benefits Metrics */}
        {/* <div className={styles.benefitsMetrics}>
          <h3 className={styles.metricsTitle}>
            <Translate id="education.future.solutions.benefits.title">
              Proven Impact on Learning Outcomes
            </Translate>
          </h3>  
          <div className={styles.metricsGrid}>
            {benefits.map((benefit, index) => (
              <div key={index} className={styles.metricCard}>
                <span className={styles.metricIcon}>{benefit.icon}</span>
                <h4 className={styles.metricTitle}>
                  <Translate id={benefit.titleId} />
                </h4>
                <p className={styles.metricDescription}>
                  <Translate id={benefit.descriptionId} />
                </p>
              </div>
            ))}
          </div> 
        </div>*/}



        {/* How It Works */}
        <div className={styles.howItWorks}>
          <div className={styles.howItWorksImage}>
            <img
              className={styles.featureImage}
              alt="FunBlocks AIFlow benefits compared to ChatGPT"
              src="/img/portfolio/thumbnails/aiflow_benefits.png"
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/aiflow_benefits.png")}
            />
          </div>
          <div className={styles.howItWorksContent}>
            <h3 className={styles.howItWorksTitle}>
              <Translate id="education.future.solutions.howItWorks.title">
                How FunBlocks AI Enhances Higher-Order Thinking
              </Translate>
            </h3>
            <div className={styles.workflowSteps}>
              <div className={styles.workflowStep}>
                <div className={styles.stepHeader}>
                  <span className={styles.stepNumber}>1</span>
                  <h4 className={styles.stepTitle}>
                    <Translate id="education.future.solutions.workflow.step1.title">
                      Structured Frameworks
                    </Translate>
                  </h4>
                </div>
                <p className={styles.stepDescription}>
                  <Translate id="education.future.solutions.workflow.step1.description">
                    Built-in educational frameworks like Bloom's Taxonomy guide thinking processes
                  </Translate>
                </p>
              </div>
              <div className={styles.workflowStep}>
                <div className={styles.stepHeader}>
                  <span className={styles.stepNumber}>2</span>
                  <h4 className={styles.stepTitle}>
                    <Translate id="education.future.solutions.workflow.step2.title">
                      AI-Assisted Exploration
                    </Translate>
                  </h4>
                </div>
                <p className={styles.stepDescription}>
                  <Translate id="education.future.solutions.workflow.step2.description">
                    AI helps break down complex topics and suggests new perspectives
                  </Translate>
                </p>
              </div>
              <div className={styles.workflowStep}>
                <div className={styles.stepHeader}>
                  <span className={styles.stepNumber}>3</span>
                  <h4 className={styles.stepTitle}>
                    <Translate id="education.future.solutions.workflow.step3.title">
                      Visual Synthesis
                    </Translate>
                  </h4>
                </div>
                <p className={styles.stepDescription}>
                  <Translate id="education.future.solutions.workflow.step3.description">
                    Transform ideas into visual formats that enhance understanding and retention
                  </Translate>
                </p>
              </div>
              <div className={styles.workflowStep}>
                <div className={styles.stepHeader}>
                  <span className={styles.stepNumber}>4</span>
                  <h4 className={styles.stepTitle}>
                    <Translate id="education.future.solutions.workflow.step4.title">
                      Reflective Practice
                    </Translate>
                  </h4>
                </div>
                <p className={styles.stepDescription}>
                  <Translate id="education.future.solutions.workflow.step4.description">
                    Built-in reflection tools help consolidate learning and develop metacognition
                  </Translate>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}





// Educational Transformation Section Component
function EducationalTransformationSection() {
  const transformationAspects = [
    {
      titleId: 'education.future.transformation.curriculum.title',
      descriptionId: 'education.future.transformation.curriculum.description',
      icon: '📚',
      changes: [
        'education.future.transformation.curriculum.change1',
        'education.future.transformation.curriculum.change2',
        'education.future.transformation.curriculum.change3'
      ]
    },
    {
      titleId: 'education.future.transformation.pedagogy.title',
      descriptionId: 'education.future.transformation.pedagogy.description',
      icon: '🎯',
      changes: [
        'education.future.transformation.pedagogy.change1',
        'education.future.transformation.pedagogy.change2',
        'education.future.transformation.pedagogy.change3'
      ]
    },
    {
      titleId: 'education.future.transformation.assessment.title',
      descriptionId: 'education.future.transformation.assessment.description',
      icon: '📊',
      changes: [
        'education.future.transformation.assessment.change1',
        'education.future.transformation.assessment.change2',
        'education.future.transformation.assessment.change3'
      ]
    },
    {
      titleId: 'education.future.transformation.roles.title',
      descriptionId: 'education.future.transformation.roles.description',
      icon: '👥',
      changes: [
        'education.future.transformation.roles.change1',
        'education.future.transformation.roles.change2',
        'education.future.transformation.roles.change3'
      ]
    }
  ];

  return (
    <section className={clsx(styles.section, styles.transformationSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.future.transformation.title">
            The Great Educational Transformation
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.future.transformation.description">
            AI is not just changing how we teach and learn—it's fundamentally transforming what education means. This transformation touches every aspect of the educational ecosystem.
          </Translate>
        </p>

        <div className={styles.transformationGrid}>
          {transformationAspects.map((aspect, index) => (
            <div key={index} className={styles.transformationCard}>
              <div className={styles.transformationHeader}>
                <span className={styles.transformationIcon}>{aspect.icon}</span>
                <h3 className={styles.transformationTitle}>
                  <Translate id={aspect.titleId} />
                </h3>
              </div>
              <p className={styles.transformationDescription}>
                <Translate id={aspect.descriptionId} />
              </p>
              <ul className={styles.transformationChanges}>
                {aspect.changes.map((changeId, changeIndex) => (
                  <li key={changeIndex}>
                    <Translate id={changeId} />
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className={styles.transformationVision}>
          <h3 className={styles.visionTitle}>
            <Translate id="education.future.transformation.vision.title">
              Our Vision for the Future of Education
            </Translate>
          </h3>
          <p className={styles.visionText}>
            <Translate id="education.future.transformation.vision.text">
              We envision an educational future where AI amplifies human potential, where every learner can access personalized, engaging, and meaningful learning experiences, and where educators are empowered to focus on what they do best: inspiring, mentoring, and nurturing the next generation of thinkers and innovators.
            </Translate>
          </p>
        </div>
      </div>
    </section>
  );
}

// Product Solutions Section Component
function ProductSolutionsSection() {
  const productSolutions = [
    {
      titleId: 'education.products.aiflow.title',
      descriptionId: 'education.products.aiflow.description',
      icon: '💡',
      link: 'https://www.funblocks.net/aiflow',
      features: [
        'education.products.aiflow.feature1',
        'education.products.aiflow.feature2',
        'education.products.aiflow.feature3'
      ],
      category: 'thinking'
    },
    {
      titleId: 'education.products.workspace.title',
      descriptionId: 'education.products.workspace.description',
      icon: '🛠️',
      link: 'https://www.funblocks.net',
      features: [
        'education.products.workspace.feature1',
        'education.products.workspace.feature2',
        'education.products.workspace.feature3'
      ],
      category: 'productivity'
    },
    {
      titleId: 'education.products.aitools.title',
      descriptionId: 'education.products.aitools.description',
      icon: '🎯',
      link: 'https://www.funblocks.net/aitools',
      features: [
        'education.products.aitools.feature1',
        'education.products.aitools.feature2',
        'education.products.aitools.feature3'
      ],
      category: 'education'
    }
  ];

  return (
    <section id="products-solutions" className={clsx(styles.section, styles.productSolutionsSection)}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="education.products.title">
            Complete AI Education Solutions for the Future
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="education.products.description">
            Addressing the educational transformation and AI literacy challenges of our time, FunBlocks AI provides a comprehensive suite of products that work together to create a complete learning ecosystem for the AI era.
          </Translate>
        </p>

        <div className={styles.productSolutionsGrid}>
          {productSolutions.map((product, index) => (
            <div key={index} className={clsx(styles.productCard, styles[`product-${product.category}`])}>
              <div className={styles.productHeader}>
                <span className={styles.productIcon}>{product.icon}</span>
                <h3 className={styles.productTitle}>
                  <Translate id={product.titleId} />
                </h3>
              </div>
              <p className={styles.productDescription}>
                <Translate id={product.descriptionId} />
              </p>
              <ul className={styles.productFeatures}>
                {product.features.map((featureId, featureIndex) => (
                  <li key={featureIndex}>
                    <span className={styles.featureIcon}>✨</span>
                    <Translate id={featureId} />
                  </li>
                ))}
              </ul>
              <Link to={product.link} className={styles.productLink}>
                <Translate id="education.products.explore">
                  Explore Product →
                </Translate>
              </Link>
            </div>
          ))}
        </div>

        <div className={styles.solutionIntegration}>
          <h3 className={styles.integrationTitle}>
            <Translate id="education.products.integration.title">
              How Our Solutions Work Together
            </Translate>
          </h3>
          <div className={styles.integrationFlow}>
            <div className={styles.flowStep}>
              <span className={styles.stepIcon}>🎯</span>
              <h4>
                <Translate id="education.products.integration.step1.title">
                  Specialized AI Tools
                </Translate>
              </h4>
              <p>
                <Translate id="education.products.integration.step1.description">
                  Use BloomBrain, MarzanoBrain, and other cognitive framework tools for targeted skill development
                </Translate>
              </p>
            </div>
            <div className={styles.flowArrow}>→</div>
            <div className={styles.flowStep}>
              <span className={styles.stepIcon}>💡</span>
              <h4>
                <Translate id="education.products.integration.step2.title">
                  Visual Thinking
                </Translate>
              </h4>
              <p>
                <Translate id="education.products.integration.step2.description">
                  Transform insights into mind maps and visual knowledge structures with AIFlow
                </Translate>
              </p>
            </div>
            <div className={styles.flowArrow}>→</div>
            <div className={styles.flowStep}>
              <span className={styles.stepIcon}>🛠️</span>
              <h4>
                <Translate id="education.products.integration.step3.title">
                  Complete Workspace
                </Translate>
              </h4>
              <p>
                <Translate id="education.products.integration.step3.description">
                  Create comprehensive learning materials and collaborate in the all-in-one workspace
                </Translate>
              </p>
            </div>
          </div>
        </div>

        <div className={styles.solutionBenefits}>
          <h3 className={styles.benefitsTitle}>
            <Translate id="education.products.benefits.title">
              Why Choose FunBlocks AI Education Solutions
            </Translate>
          </h3>
          <div className={styles.benefitsGrid}>
            <div className={styles.benefitItem}>
              <span className={styles.benefitIcon}>🎓</span>
              <h4>
                <Translate id="education.products.benefits.comprehensive.title">
                  Comprehensive Coverage
                </Translate>
              </h4>
              <p>
                <Translate id="education.products.benefits.comprehensive.description">
                  From individual thinking tools to complete learning environments, we cover every aspect of AI-enhanced education
                </Translate>
              </p>
            </div>
            <div className={styles.benefitItem}>
              <span className={styles.benefitIcon}>🔬</span>
              <h4>
                <Translate id="education.products.benefits.research.title">
                  Research-Based
                </Translate>
              </h4>
              <p>
                <Translate id="education.products.benefits.research.description">
                  Every feature is grounded in educational research and cognitive science principles
                </Translate>
              </p>
            </div>
            <div className={styles.benefitItem}>
              <span className={styles.benefitIcon}>🚀</span>
              <h4>
                <Translate id="education.products.benefits.future.title">
                  Future-Ready
                </Translate>
              </h4>
              <p>
                <Translate id="education.products.benefits.future.description">
                  Prepare students and educators for the AI era with cutting-edge tools and methodologies
                </Translate>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// CTA Section Component
function CTASection() {
  return (
    <section className={clsx(styles.section, styles.ctaSection)}>
      <div className="container">
        <div className={styles.ctaContent}>
          <Heading as="h2" className={styles.ctaTitle}>
            <Translate id="education.future.cta.title">
              Ready to Shape the Future of Education?
            </Translate>
          </Heading>
          <p className={styles.ctaDescription}>
            <Translate id="education.future.cta.description">
              Join thousands of educators and learners who are already using FunBlocks AI to enhance their thinking capabilities and prepare for the future. Start your journey toward more effective, engaging, and meaningful education today.
            </Translate>
          </p>
          <div className={styles.ctaButtons}>
            <Link
              to="https://www.funblocks.net/aiflow"
              className={clsx(styles.ctaButton, styles.ctaPrimary)}
            >
              <Translate id="education.future.cta.primary">FunBlocks AIFlow</Translate>
            </Link>
            <Link
              to="https://www.funblocks.net/aitools"
              className={clsx(styles.ctaButton, styles.ctaSecondary)}
            >
              <Translate id="education.future.cta.secondary">AI Tools</Translate>
            </Link>
          </div>
          <div className={styles.ctaFeatures}>
            <div className={styles.ctaFeature}>
              <span>✓</span>
              <Translate id="education.future.cta.feature1">
                Free access to all core features
              </Translate>
            </div>
            <div className={styles.ctaFeature}>
              <span>✓</span>
              <Translate id="education.future.cta.feature2">
                No credit card required
              </Translate>
            </div>
            <div className={styles.ctaFeature}>
              <span>✓</span>
              <Translate id="education.future.cta.feature3">
                Start using immediately
              </Translate>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function EducationPage() {
  const [showImageSrc, setShowImageSrc] = useState(null);

  return (
    <Layout
      title={translate({
        id: 'education.future.hero.title',
        message: 'FunBlocks AI: Shaping the Future of Education',
      })}
      description={translate({
        id: 'education.future.hero.description',
        message: 'In the AI era, education faces unprecedented transformation. FunBlocks AI empowers educators and learners with advanced thinking tools to develop essential skills for the future, fostering human-AI collaboration that enhances rather than replaces human intelligence.',
      })}
    >
      <EducationStructuredData />
      <HeroSection />
      <FunBlocksEducationBenefitsSection />
      <BenefitsForUsersSection />
      <AIEraImpactSection />
      <HumanAICollaborationSection />
      <AIEraSkillsSection />
      <HigherOrderThinkingSection setShowImageSrc={setShowImageSrc} />
      <FunBlocksSolutionsSection />
      <EducationalTransformationSection />
      <ProductSolutionsSection />
      <EducationFAQ />
      <CTASection />
      <Footer />
      {showImageSrc && <ImageModal imageSrc={showImageSrc} setImageSrc={setShowImageSrc} />}
    </Layout>
  );
}
